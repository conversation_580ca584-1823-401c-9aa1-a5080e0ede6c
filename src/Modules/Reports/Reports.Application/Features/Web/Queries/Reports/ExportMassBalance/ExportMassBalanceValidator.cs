using FluentValidation;

namespace Reports.Application.Features.Web.Queries.Reports.ExportMassBalance;

public class ExportMassBalanceValidator : AbstractValidator<ExportMassBalanceRequest>
{
    public ExportMassBalanceValidator()
    {
        RuleFor(x => x.FromDate)
            .NotEmpty()
            .WithMessage("FromDate is required");

        RuleFor(x => x.ToDate)
            .NotEmpty()
            .WithMessage("ToDate is required")
            .GreaterThan(x => x.FromDate)
            .WithMessage("ToDate must be greater than FromDate");
    }
}
